using Microsoft.UI.Xaml;
using Microsoft.UI.Xaml.Controls;
using Microsoft.UI.Xaml.Controls.Primitives;
using Microsoft.UI.Xaml.Data;
using Microsoft.UI.Xaml.Input;
using Microsoft.UI.Xaml.Media;
using Microsoft.UI.Xaml.Navigation;
using System;
using System.Collections.Generic;
using Windows.Storage;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices.WindowsRuntime;
using Windows.ApplicationModel.DataTransfer;
using Windows.Foundation;
using Windows.Foundation.Collections;
using Windows.System;
using Microsoft.Extensions.DependencyInjection;
using CommunityToolkit.Mvvm.DependencyInjection;
using System.Collections.Specialized;
using System.Diagnostics;
using System.Threading.Tasks;
// To learn more about WinUI, the WinUI project structure,
// and more about our project templates, see: http://aka.ms/winui-project-info.

namespace Watcher
{
    /// <summary>
    /// An empty page that can be used on its own or navigated to within a Frame.
    /// </summary>
    public sealed partial class Overview : Page
    {
        public OverviewViewModel ViewModel { get; set; }
        private bool _isScrollPending = false; // 防止重复滚动操作

        public Overview()
        {
            ViewModel = Ioc.Default.GetRequiredService<OverviewViewModel>();
            this.DataContext = ViewModel;
            this.InitializeComponent();
            
            // 设置 DispatcherQueue 用于UI线程调度
            ViewModel.SetDispatcherQueue(this.DispatcherQueue);

            // 订阅ChargeItems集合变化事件，当添加新项目时自动滚动到底部
            ViewModel.ChargeItems.CollectionChanged += ChargeItems_CollectionChanged;
            
            // 绑定删除按钮点击事件
            DeleteButton.Click += DeleteButton_Click;
        }
        
        /// <summary>
        /// 当ChargeItems集合发生变化时，自动滚动到列表底部
        /// </summary>
        private void ChargeItems_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            // 只有当添加新项目时，才滚动到最底部
            if (e.Action == NotifyCollectionChangedAction.Add && e.NewItems != null && e.NewItems.Count > 0)
            {
                // 防止重复滚动操作
                if (_isScrollPending)
                    return;

                _isScrollPending = true;

                // 使用Dispatcher确保UI操作在UI线程上执行，并添加延迟以等待UI更新完成
                DispatcherQueue.TryEnqueue(() =>
                {
                    // 添加小延迟，确保ListView完全更新后再滚动
                    Task.Delay(50).ContinueWith(_ =>
                    {
                        DispatcherQueue.TryEnqueue(() =>
                        {
                            try
                            {
                                // 滚动到新添加的最后一个项目
                                var lastAddedItem = e.NewItems[e.NewItems.Count - 1];
                                ChargeItemsListBox.ScrollIntoView(lastAddedItem);
                                
                                // 自动选中最新添加的项目
                                ChargeItemsListBox.SelectedItem = lastAddedItem;
                            }
                            catch (Exception ex)
                            {
                                // 忽略滚动错误，避免影响用户体验
                                Debug.WriteLine($"滚动操作失败: {ex.Message}");
                            }
                            finally
                            {
                                _isScrollPending = false;
                            }
                        });
                    });
                });
            }
        }

        private void DeleteButton_Click(object sender, RoutedEventArgs e)
        {
            // 获取选中的项目
            var selectedItems = ChargeItemsListBox.SelectedItems;
            
            if (selectedItems != null && selectedItems.Count > 0)
            {
                // 调用ViewModel的删除方法
                ViewModel.DeleteSelectedItems(selectedItems);
            }
        }

        private async void ExportButton_Click(object sender, RoutedEventArgs e)
        {
            var defaultExportFolder = ViewModel._configService.GetLastExportFolder();
            var fileName = $"收费记录_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx";
            var savePath = Path.Combine(defaultExportFolder, fileName);
            
            try
            {
                if (ViewModel.ChargeItems.Count == 0)
                {
                    // 显示无记录提示
                    var noDataDialog = new ContentDialog
                    {
                        Title = "提示",
                        Content = "当前没有收费记录可导出",
                        CloseButtonText = "确定",
                        XamlRoot = this.XamlRoot
                    };
                    await noDataDialog.ShowAsync();
                    return;
                }

                // 确保导出目录存在
                var exportDir = Path.GetDirectoryName(savePath);
                if (!string.IsNullOrEmpty(exportDir))
                {
                    Directory.CreateDirectory(exportDir);
                    
                    // 更新最后导出的文件夹路径
                    ViewModel._configService.UpdateLastExportFolder(exportDir);
                }
                // 导出Excel
                bool success = await ViewModel.ExportToExcelAsync(savePath);

                if (success)
                {
                    // 创建自定义对话框内容
                    var dialogContent = new StackPanel();
                    dialogContent.HorizontalAlignment = HorizontalAlignment.Center;
                    dialogContent.Children.Add(new TextBlock
                    {
                        Text = $"收费记录已成功导出到:\n{savePath}",
                        TextWrapping = Microsoft.UI.Xaml.TextWrapping.Wrap
                    });

                    // 创建按钮容器
                    var buttonPanel = new StackPanel
                    {
                        Orientation = Orientation.Horizontal,
                        Spacing = 10,
                        Margin = new Microsoft.UI.Xaml.Thickness(0, 20, 0, 0)
                    };

                    // 打开文件夹按钮
                    var openFolderButton = new Button
                    {
                        Content = "打开文件所在文件夹",
                        HorizontalAlignment = Microsoft.UI.Xaml.HorizontalAlignment.Left
                    };
                    openFolderButton.Click += async (s, e) =>
                    {
                        try
                        {
                            var folderPath = Path.GetDirectoryName(savePath);
                            if (!string.IsNullOrEmpty(folderPath))
                            {
                                await Windows.System.Launcher.LaunchFolderPathAsync(folderPath);
                            }
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"打开文件夹失败: {ex.Message}");
                        }
                    };

                    // 复制文件按钮
                    var copyFileButton = new Button
                    {
                        Content = "复制文件",
                        HorizontalAlignment = Microsoft.UI.Xaml.HorizontalAlignment.Left
                    };
                    copyFileButton.Click += async (s, e) =>
                    {
                        try
                        {
                            // 创建文件数据包
                            var dataPackage = new Windows.ApplicationModel.DataTransfer.DataPackage();
                            var storageFile = await Windows.Storage.StorageFile.GetFileFromPathAsync(savePath);
                            dataPackage.SetStorageItems(new List<Windows.Storage.IStorageItem> { storageFile });
                            Windows.ApplicationModel.DataTransfer.Clipboard.SetContent(dataPackage);
                            
                            // 可选：显示复制成功提示（使用轻量级通知而非对话框）
                            System.Diagnostics.Debug.WriteLine("文件已复制到剪贴板");
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"复制文件失败: {ex.Message}");
                        }
                    };

                    buttonPanel.Children.Add(openFolderButton);
                    buttonPanel.Children.Add(copyFileButton);
                    dialogContent.Children.Add(buttonPanel);

                    // 显示成功消息
                    var successDialog = new ContentDialog
                    {
                        Title = "导出成功",
                        Content = dialogContent,
                        CloseButtonText = "确定",
                        XamlRoot = this.XamlRoot
                    };
                    
                    // 注意：这里不等待对话框关闭，让按钮事件处理程序异步执行
                    _ = successDialog.ShowAsync();
                }
                else
                {
                    // 显示失败消息
                    var errorDialog = new ContentDialog
                    {
                        Title = "导出失败",
                        Content = "导出Excel文件时出现错误，请重试",
                        CloseButtonText = "确定",
                        XamlRoot = this.XamlRoot
                    };
                    await errorDialog.ShowAsync();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"导出操作失败: {ex.Message}");

                // 显示错误消息
                var errorDialog = new ContentDialog
                {
                    Title = "操作失败",
                    Content = $"导出过程中出现错误: {ex.Message}",
                    CloseButtonText = "确定",
                    XamlRoot = this.XamlRoot
                };
                await errorDialog.ShowAsync();
            }
        }

    }
}
